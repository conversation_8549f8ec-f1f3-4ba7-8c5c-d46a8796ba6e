# Sherlock Agent - Streamlit UI

## Overview

A user-friendly Streamlit interface for the Sherlock Agent system, providing an intuitive way to interact with AFEX's AI Data Analyst.

## Features

### 🎯 **Core Features**
- **Interactive Chat Interface**: Natural language conversations with <PERSON>
- **Session Management**: Create and manage multiple chat sessions
- **SQL Query Display**: View generated SQL queries with syntax highlighting
- **Data Visualization**: Automatic charts and graphs for query results
- **Real-time Metrics**: Track queries executed and rows returned

### 🔧 **User Interface Components**

#### **Sidebar**
- **Session Management**: Create new sessions, switch between sessions
- **Session Statistics**: Message count, queries executed, total rows
- **Agent Status**: Real-time agent initialization status
- **System Information**: Help and guidance

#### **Main Interface**
- **Chat History**: Scrollable conversation history
- **Message Display**: Styled user and agent messages
- **SQL Query Viewer**: Formatted SQL with syntax highlighting
- **Data Tables**: Interactive pandas DataFrames
- **Auto Visualizations**: Charts generated from numeric data
- **Input Area**: Text area for user questions

### 📊 **Data Visualization**
- **Automatic Chart Generation**: Based on data types
- **Interactive Tables**: Sortable and filterable DataFrames
- **Metrics Display**: Row counts, column counts, execution times
- **Export Capabilities**: Download results as CSV

## Installation

### Prerequisites
- Python 3.8+
- All agent dependencies installed
- Environment variables configured (.env file)

### Install Streamlit Dependencies
```bash
pip install plotly  # Only additional dependency needed
```

### Required Packages
- `streamlit>=1.28.0` (already installed)
- `plotly>=5.15.0` (install with pip install plotly)
- `pandas>=2.0.0` (already available)
- All existing agent dependencies

## Usage

### Quick Start Options

#### Option 1: Demo Script (Recommended for first time)
```bash
python demo_ui.py
```
This will:
- Check all prerequisites
- Show usage instructions
- Launch the UI with guidance

#### Option 2: Direct Launch
```bash
# Windows
start_ui.bat

# Cross-platform
python run_ui.py

# Direct streamlit command
streamlit run streamlit_app.py
```

### Access the UI
1. Open your web browser
2. Navigate to `http://localhost:8501`
3. The interface will load automatically

### Basic Workflow
1. **Initialize Agent**: The agent initializes automatically on startup
2. **Create Session**: Click "➕ New Session" in the sidebar
3. **Ask Questions**: Type your data questions in the input area
4. **View Results**: See SQL queries, data tables, and visualizations
5. **Manage Sessions**: Switch between sessions or create new ones

## Interface Guide

### 🔍 **Main Header**
- Displays "Sherlock Agent - AFEX AI Data Analyst"
- Shows current status and branding

### 📝 **Session Management**
```
Sidebar → Sessions
├── ➕ New Session (Create new chat session)
├── Session Selector (Dropdown to switch sessions)
└── 📊 Session Stats
    ├── Messages count
    ├── Queries executed
    └── Total rows returned
```

### 💬 **Chat Interface**
- **User Messages**: Blue-themed with user icon
- **Agent Messages**: Purple-themed with Sherlock icon
- **Timestamps**: Show when each message was sent
- **SQL Queries**: Highlighted code blocks
- **Data Results**: Interactive tables and charts

### 🎨 **Styling Features**
- **Responsive Design**: Works on desktop and mobile
- **Color-coded Messages**: Easy to distinguish user vs agent
- **Syntax Highlighting**: SQL queries with proper formatting
- **Interactive Elements**: Hover effects and smooth transitions

## Example Usage

### Sample Questions
```
"What is the total number of farmers?"
"Show me the top 10 commodities by volume"
"What are the average loan amounts by region?"
"Display client segments and their counts"
"Show me recent transactions over $10,000"
```

### Expected Workflow
1. **User Input**: "What is the total number of farmers?"
2. **Agent Processing**: Sherlock analyzes the question
3. **SQL Generation**: `SELECT COUNT(*) FROM dim_farmer`
4. **Query Execution**: Runs against the database
5. **Results Display**: Shows count with visualization
6. **Agent Response**: Natural language explanation

## Features in Detail

### 🔄 **Session Management**
- **Multiple Sessions**: Work on different analyses simultaneously
- **Session Persistence**: Conversations saved during browser session
- **Session Statistics**: Track productivity and usage
- **Easy Switching**: Quick navigation between sessions

### 📊 **Data Display**
- **Interactive Tables**: Sort, filter, and explore data
- **Auto Visualizations**: Charts generated based on data types
- **Metrics Cards**: Quick stats about query results
- **Export Options**: Download data for further analysis

### 🎯 **User Experience**
- **Real-time Updates**: Immediate feedback and responses
- **Error Handling**: Clear error messages and recovery
- **Loading Indicators**: Progress feedback during processing
- **Responsive Design**: Works on various screen sizes

## Configuration

### Environment Variables
Ensure your `.env` file contains:
```env
POSTGRES_HOST=your_host
POSTGRES_PORT=5432
POSTGRES_DATABASE=your_db
POSTGRES_USERNAME=your_user
POSTGRES_PASSWORD=your_password
OPENAI_API_KEY=your_openai_key
```

### Streamlit Configuration
The app uses these default settings:
- **Port**: 8501
- **Host**: localhost
- **Auto-reload**: Enabled
- **Usage Stats**: Disabled

## Troubleshooting

### Common Issues

#### **Agent Not Initializing**
```
Problem: "Agent Not Initialized" in sidebar
Solution: 
1. Check database connection
2. Verify environment variables
3. Click "🔄 Initialize Agent" button
```

#### **Database Connection Errors**
```
Problem: Connection timeouts or failures
Solution:
1. Verify database is running
2. Check network connectivity
3. Validate credentials in .env file
```

#### **Import Errors**
```
Problem: Module not found errors
Solution:
1. Install requirements: pip install -r requirements.txt
2. Check Python path
3. Ensure agent package is importable
```

### Debug Mode
Enable debug logging by setting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Tips

### 🚀 **Optimization**
- **Session Limits**: Limit to 5-10 active sessions
- **Message History**: Clear old sessions periodically
- **Data Caching**: Streamlit caches query results automatically
- **Browser Performance**: Use modern browsers for best experience

### 📈 **Monitoring**
- Check `logs/` directory for detailed application logs
- Monitor database connection status in sidebar
- Track session statistics for usage patterns

## Development

### Adding Features
1. **New Components**: Add to `streamlit_app.py`
2. **Styling**: Modify CSS in the `st.markdown()` sections
3. **Functionality**: Extend the agent integration
4. **Testing**: Use the session management for testing

### Custom Styling
Modify the CSS in the `st.markdown()` block:
```python
st.markdown("""
<style>
    /* Your custom styles here */
</style>
""", unsafe_allow_html=True)
```

## Security Considerations

- **Environment Variables**: Never commit `.env` files
- **Database Access**: Uses existing agent security measures
- **Session Data**: Stored in browser memory only
- **API Keys**: Handled through agent configuration

## Future Enhancements

- **User Authentication**: Login system for multi-user access
- **Session Persistence**: Save sessions to database
- **Advanced Visualizations**: More chart types and customization
- **Export Features**: PDF reports and data exports
- **Admin Panel**: System monitoring and configuration

This Streamlit UI provides a professional, user-friendly interface for interacting with the Sherlock Agent, making it easy for users to analyze AFEX's data through natural language conversations.
