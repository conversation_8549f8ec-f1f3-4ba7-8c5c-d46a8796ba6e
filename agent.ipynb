import getpass
import os


def _set_env(key: str):
    if key not in os.environ:
        
        os.environ[key] = getpass.getpass(f"{key}:")
        return os.environ[key]

openai_api_key = _set_env("OPENAI_API_KEY")

import re

def extract_columns(table_desc: str):
    """
    Extracts column names and data types from a table description string.
    Returns a list of dictionaries with 'name' and 'data_type'.
    Only captures lines with both name and data_type, ignores description.
    """
    pattern = re.compile(
        r"\s*name:\s*(\w+)\s*\n\s*data_type:\s*([^\n]+?)(?:\n|$)", re.MULTILINE
    )
    columns = []
    for match in pattern.finditer(table_desc):
        columns.append({
            "name": match.group(1),
            "data_type": match.group(2).strip()
        })
    return columns

# columns = extract_columns(res)
# columns[:5]  # Show first 5 columns


from langchain_core.documents import Document
import json

def format_doc (desc):

    names = desc[0].lstrip("SH_").split('.')
    
    if names[0] == 'exchange_mart':
        source_system = 'Exchange'
    elif names[0] == 'trade_mart':
        source_system = 'Workbench'
    else:
        source_system = 'Unknown'
    desc = desc[1]
    columns = extract_columns(desc)
    meta = {'schema': names[0], 'table': names[1], 'source': source_system, 'columns': columns}
    return Document(page_content=desc, metadata=meta)

with open("schema_desc.json") as f:
    descs = json.load(f)

documents = [format_doc(desc) for desc in descs]

len(documents)

documents

from psycopg2 import connect
from psycopg2.errors import OperationalError, ProgrammingError, SyntaxError, UndefinedTable, UndefinedColumn, InvalidTextRepresentation
from psycopg2.sql import SQL, Identifier, Literal
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import HttpUrl, Field, IPvAnyAddress
from contextlib import contextmanager
from sqlalchemy import create_engine, text

class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8",
                                      case_sensitive=False, extra="forbid")
    POSTGRES_HOST: IPvAnyAddress = Field()
    POSTGRES_PORT: int = Field(...)
    POSTGRES_DATABASE: str = Field(min_length=5)
    POSTGRES_USERNAME: str = Field(min_length=5)
    POSTGRES_PASSWORD: str = Field(...)
    OPENAI_API_KEY: str = Field(...)
    SUPABASE_PASSWORD: str = Field(...)
    SUPABASE_CONN_STRING: str = Field(...)
    SUPABASE_COLLECTION_NAME: str = Field(...)
    SCHEMA_DEFINIOTIONS_PATH: str = Field(default="schema_desc.json")
    BUSINESS_RULE_PATH: str = Field(default="business_rules.yml")
    QDRANT_API_KEY: str = Field(...)
    QDRANT_URL: str = Field(...)

settings = Settings()


def connect_db():
    engine = create_engine(f"postgresql://{settings.POSTGRES_USERNAME}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}")
    return engine.connect()
    # return connect(
    #     host=settings.POSTGRES_HOST,
    #     port=settings.POSTGRES_PORT,
    #     database=settings.POSTGRES_DATABASE,
    #     user=settings.POSTGRES_USERNAME,
    #     password=settings.POSTGRES_PASSWORD,
    # )

def get_engine():
    engine = create_engine(f"postgresql://{settings.POSTGRES_USERNAME}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}")
    return engine
    # return connect(
    #     host=settings.POSTGRES_HOST,
    #     port=settings.POSTGRES_PORT,
    #     database=settings.POSTGRES_DATABASE,
    #     user=settings.POSTGRES_USERNAME,
    #     password=settings.POSTGRES_PASSWORD,
    # )

engine = get_engine()

from langchain_core.vectorstores import InMemoryVectorStore
from langchain_community.vectorstores import SupabaseVectorStore

from langchain_openai.embeddings import OpenAIEmbeddings

embeddings = OpenAIEmbeddings(openai_api_key=openai_api_key)

import vecs
SUPABASE_PASSWORD= settings.SUPABASE_PASSWORD
SUPABASE_CONN_STRING = settings.SUPABASE_CONN_STRING
DIMENSION = 1536
collection = settings.SUPABASE_COLLECTION_NAME
connection_string = SUPABASE_CONN_STRING.format(SUPABASE_PASSWORD=SUPABASE_PASSWORD)
vx =  vecs.create_client(connection_string)
docs = vx.get_or_create_collection(name=collection, dimension=DIMENSION)

from langchain_qdrant import QdrantVectorStore
url = "https://bfcb1516-fc00-4139-9d84-a1a4ab7da7b1.europe-west3-0.gcp.cloud.qdrant.io:6333"
docs = []  # put docs here
qdrant = QdrantVectorStore.from_documents(
    documents,
    embeddings,
    url=settings.QDRANT_URL,
    prefer_grpc=True,
    api_key=settings.QDRANT_API_KEY,
    collection_name="dw_schema_v1",
)

def get_retriever(collection_name: str, embeddings, **kwargs):
    """
    Connect to an existing Qdrant collection and return a retriever
    for use in LLM-based RAG pipelines.
    """
    vs = QdrantVectorStore.from_existing_collection(
        embedding=embeddings,
        collection_name=collection_name,
        url=settings.QDRANT_URL, api_key=settings.QDRANT_API_KEY,
    )
    retriever = vs.as_retriever(**kwargs)
    return retriever


schema_retriever = get_retriever("dw_schema_v1", embeddings, top_k=3)

res = schema_retriever.invoke("How many farmers do we have")

q = SQL("PREPARE validate_sql AS SELECT COUNT(*) AS total_farmers FROM trade_mart.dim_farmer WHERE is_deleted IS FALSE OR is_deleted IS NULL;")

from psycopg2.errors import (
    DatabaseError,
    OperationalError,
    InterfaceError,
)
from sqlalchemy.exc import (
    NoSuchTableError, NoSuchColumnError, 
    ProgrammingError, DataError, 
    InterfaceError, OperationalError, 
    DatabaseError
)

def validate_sql_query(query: str) -> dict:
    """
    Validates a SQL query for syntax and reference correctness using EXPLAIN.
    Assumes a global `conn` exists.

    Returns:
        dict with keys:
            - 'valid' (bool): True if query is valid
            - 'error' (str or None): Error message if invalid
            - 'type' (str or None): Type of error ('query', 'connection', etc.)
    """
    try:
        # cur = conn.cursor()
        with engine.connect() as conn:
            conn.execute(text(f"EXPLAIN {query}"))
            return {'valid': True, 'error': None, 'type': None}

    # Query-level errors (these are expected during validation)

    except Exception as e:
        if isinstance(e, (NoSuchTableError, NoSuchColumnError, 
            ProgrammingError, DataError)):

            return {'valid': False, 'error': str(e).strip(), 'type': 'query'}
        
        
        elif isinstance(e, InterfaceError):
            return {'valid': False, 'error': str(e).strip(), 'type': 'connection'}
        
        # Connection-level errors (shouldn't be handled silently)
        elif isinstance(e, (OperationalError, DatabaseError)):

            return {'valid': False, 'error': str(e).strip(), 'type': 'connection'}
        else:
            
            return {'valid': False, 'error': str(e).strip(), 'type': 'unknown'}



from enum import Enum
from pydantic import BaseModel, Field, field_validator
from langgraph.graph import StateGraph
# from langgraph.prebuilt import ToolNode
from langgraph.graph.message import add_messages
from typing import Annotated, TypedDict

from langchain_openai.chat_models import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage




class UserQuery(BaseModel):
    query: str = Field(..., description="The user's query")

class VectorSearchQuery(UserQuery):
    query_str: str = Field(..., description="The query string to search for")
    top_k: int = 3
    metadata: dict = {}

class SQLQuery(BaseModel):
    sql_query: str = Field(..., description="The SQL query to run")
    
    @field_validator("sql_query")
    @classmethod
    def validate_sql(cls, v):
        if not v.startswith("SELECT"):
            raise ValueError("SQL query must start with SELECT")
        if "FROM" not in v:
            raise ValueError("SQL query must contain FROM")
        validate = validate_sql_query(v)
        if not validate['valid']:
            if validate['type']in ('connection', 'unknown'):
                raise Exception(f"Failed to validate Query: {validate['error']}")
            raise ValueError(validate['error']) 
        
        return v


class Control(Enum):
    AGENT = "AGENT"
    USER = "USER"
    TOOL = "TOOL"

class SQLResult(BaseModel):
    result: dict = Field(..., description="The result of the SQL query")
    sql_query: str = Field(..., description="The SQL query that was run")

class FailedSQLQuery(BaseModel):
    sql_query: str 
    error: str

class SQLWriteQuery(BaseModel):
    user_query: str = Field(..., description="The user's query")
    # schema_info: str = Field(..., description="The schema information provided to you")
    query_plan: str = Field(..., description="The query plan formated as yml string")

class State(TypedDict):
    messages: Annotated[list, add_messages]
    sql_query: SQLQuery
    result: dict
    query_plan: str
    schema_info: list
    command: Control = Control.AGENT


def human_action(response):
    """Returns response to the user"""
    pass

import pydantic
try:
    sql_query = SQLQuery(sql_query="SELECT COUNT(*) FROM trademart.dim_farmer WHERE is_deleted IS FALSE OR is_deleted IS NULL")
except pydantic.ValidationError as e:
    err =  e.errors()[0]['msg']
    er = e
    print(e)


er.errors()[0]['input']
er.errors()[0]


from langchain.tools.retriever import create_retriever_tool


schema_retriever.invoke("Find total nummber of farmers")

schema_retriever_tool = create_retriever_tool(
    schema_retriever,
    "schema_retriever",
    "Search for relevant schemas and tables. This uses semantic search to find the most relevant schemas and tables"
)

res = schema_retriever_tool.invoke("How many farmers do we have")

rel_docs = schema_retriever_tool.invoke("How many farmers do we have", raw=True)

rel_docs

rel_docs = schema_retriever.invoke("How many farmers do we have")
schema_defs = '\n\n---\n'.join([doc.page_content for doc in rel_docs])
tables_infor = [doc.metadata for doc in rel_docs]

tables_infor

list(filter(lambda x: x.startswith("Table Name:"), res.split('\n')))

# print(schema_retriever_tool.invoke("How many farmers do we have"))



system_prompt = SystemMessage(
    content=(
       """You are AFEX's AI Data Analyst - Sherlock, embedded within the business intelligence stack. Your role is to help stakeholders make data-informed decisions by:

    Understanding and answering business-specific questions with context from AFEX's operations (agri-finance, commodities, supply chain, etc).

    Writing efficient SQL queries targeting the analytical data warehouse to fetch relevant data.

    Executing SQL and analyzing results to provide actionable insights.

    Using available tools to:

    - Retrieve schema definitions

    - Access business glossary or metadata

    - Run queries, format results, and generate visualizations

    Always reason step-by-step:

    - Clarify ambiguous terms using internal metadata.

    - Write SQL optimized for performance and clarity.

    - Summarize key insights in business language.

    - When appropriate, plot trends or outliers using charts.
    
    
    Response style:
    - After gathering all necessary details to answer the user's query, you should respond with a final answer provide insight based on percieved user intent.

    Be concise, accurate, and grounded in AFEX's business goals. If you don't know, use available tools or ask follow-up questions."""
    )
)

# def query_executor():
#     """Executes SQL queries"""
#     pass

base_model = ChatOpenAI(model="gpt-4.1", api_key=openai_api_key)
model_with_tools = base_model.bind_tools(tools=[schema_retriever_tool, SQLWriteQuery])

def initialize_system_prompt(state):
        """Initializes the system message. The system message gives the Sherlock it's Identity and how it should behave with users"""
        if isinstance(state["messages"][0], SystemMessage):
            pass
        else:
            state["messages"].insert(0, system_prompt)

        return state

def agent(state: State):
    """Calls the base model or routes to a tool or Node"""

    initialize_system_prompt(state)
    messages = state["messages"]
    response = model_with_tools.invoke(messages)
    print("Thought: ", response.content)
    return  state | {"messages":response}




bmodel = base_model.bind_tools(tools=[SQLWriteQuery])

bmodel.invoke([HumanMessage(content="What is the total number of farmers?")])

## Define utilities
import os
import uuid
import json
import pandas as pd
from datetime import datetime

def describe_dataframe(df: pd.DataFrame) -> dict:
    non_numeric_cols = df.select_dtypes(exclude="number").columns

    if df.empty or df.shape[1] == 0:
        print("No data returned")
        return {
            "n_rows": 0,
            "n_columns": 0,
            "columns": [],
            "sample_rows": [],
            "message": "The query returned no data."
        }
    print("Dataframe columns types: ",df.dtypes)
    print(df.head())

    return {
        "columns": {col: str(dtype) for col, dtype in df.dtypes.items()},
        "nrows": df.shape[0],
        "ncolumns": df.shape[1],
        "sample_rows": df.head(5).to_dict(orient="records"),
        "null_counts": df.isnull().sum().to_dict(),
        "unique_counts": {
            col: df[col].nunique() for col in non_numeric_cols
        },
        "sample_values": {
            col: df[col].dropna().unique()[:5].tolist()
            for col in non_numeric_cols
        },
        "numeric_summary": df.select_dtypes(include="number")
                             .describe()
                             .loc[["min", "mean", "max"]]
                             .to_dict(),
        "most_frequent": {
            col: df[col].value_counts(dropna=True).head(1).to_dict()
            for col in non_numeric_cols
        }
    }



class ResultStore:
    def __init__(self, storage_dir: str = "results"):
        self.storage_dir = storage_dir
        os.makedirs(storage_dir, exist_ok=True)

    def save(self, df: pd.DataFrame, query: str, user_id: str = None, summary: str = "") -> str:
        query_id = str(uuid.uuid4())[:8]
        now = datetime.utcnow().isoformat()

        # Save result as Parquet
        result_path = os.path.join(self.storage_dir, f"{query_id}.parquet")
        df.to_parquet(result_path, index=False)

        # Save metadata
        metadata = {
            "query_id": query_id,
            "user_id": user_id,
            "query": query,
            "created_at": now,
            "row_count": len(df),
            "columns": dict(df.dtypes.apply(str)),
            "summary": summary,
            "result_path": result_path
        }
        metadata_path = os.path.join(self.storage_dir, f"{query_id}.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)

        return query_id

    def load(self, query_id: str) -> pd.DataFrame:
        path = os.path.join(self.storage_dir, f"{query_id}.parquet")
        if not os.path.exists(path):
            raise FileNotFoundError(f"No result found for query_id: {query_id}")
        return pd.read_parquet(path)

    def load_metadata(self, query_id: str) -> dict:
        path = os.path.join(self.storage_dir, f"{query_id}.json")
        if not os.path.exists(path):
            raise FileNotFoundError(f"No metadata found for query_id: {query_id}")
        with open(path) as f:
            return json.load(f)


from typing import Optional
from langchain_core.output_parsers import JsonOutputParser
import pandas as pd
import numpy as np
import pydantic
from psycopg2.extras import RealDictCursor


#  define a conditional edge
def router(state: State):
    """Routes to the appropriate node based on the user's query"""
    msg = state["messages"][-1]
    if hasattr(msg, "tool_calls") and len(msg.tool_calls) > 0:
        return "action"
    else:
        return '__end__'

def route_from_action(state: State):
    """Routes back to the agent"""
    if state["command"] == Control.AGENT:
        return 'agent'
    elif state["command"] == Control.USER:
        return "human"
    else:
        return "query_executor"
    

llm = ChatOpenAI(model="gpt-4.1", api_key=openai_api_key)
sql_prompt = SystemMessage(
    content=(
        """You are AFEX's AI Data Analyst - Sherlock, embedded within the business intelligence stack. Your primary  role is to help stakeholders make data-informed decisions by:
            Writing ayntatically correct and efficient Postgres SQL queries targeting the analytical data warehouse to fetch relevant data needed to answer the user's query.
            Pay careful attention only user releveant schema information provided to you. You must only use the tables and columns provided to you below and don't make up table or object names.

            Below is the user's query:
            {query}

            Below is the schema information provided to you:
            {schema_info}

            Write a syntaxically and semantically correct Postgres SQL query. Your response should be a valid SQL query presented in JSON format.
            {{
                "sql_query": [YOUR SQL QUERY HERE]
            }}

            Extra Note:
                - Explicitly Cast all numerical fields/ calculated fields as numeric in your query.
            """
    )
)
sql_correction_prompt = SystemMessage(
    content=(
        """ ROLE:
            You are AFEX's AI Data Analyst - Sherlock, embedded within the business intelligence stack. Your primary  role is to help stakeholders make data-informed decisions by:
            Writing a syntaxically correct and efficient Postgres SQL queries targeting the analytical data warehouse to fetch relevant data needed to answer the user's query.
            Pay careful attention only user releveant schema information provided to you. You must only use the tables and columns provided to you below and don't make up table or object names.


            Your Thought:
            I have generated the query for this question. However, it seems to have some errors. I will correct the errors and generate a new query.

            Below is the user's query:
            {query}

            Below is the schema information provided to you:
            {schema_info}

            Below is the query I generated:
            {sql_query}

            Below is the error I received:
            {error}

            I should write a syntaxically and semantically correct Postgres SQL query. I need to present my response as a valid SQL query presented in JSON format.
            {{
                "sql_query": [YOUR SQL QUERY HERE]
            }}

            Extra Note:
                - Explicitly Cast all numerical fields/ calculated fields as numeric in your query.
            """
    )
)

sql_llm = llm.with_structured_output(SQLQuery)

def generate_sql_query(args: dict, max_trials = 2, recall: bool = False, recall_info: Optional[dict] = None):
    """Generates a SQL query based on the user's query"""
    # msg = state["messages"][-1]
    # user_query = msg.content
    # error_sql_query = None
    user_query = args['user_query']
    schema_info = args['schema_info']
    if recall:
        assert recall_info, "recall_info must be provided if recall is True"
        print("Invalid SQL Query Detected. Entering Recall Stack")
        err_msg = recall_info["msg"]
        error_sql_query = recall_info["input"]
        gen_sql_prompt = sql_correction_prompt.content.format(query=user_query, schema_info=schema_info, sql_query=error_sql_query, error=err_msg)
        # print(gen_sql_prompt)
    else:
        gen_sql_prompt = sql_prompt.content.format(query=user_query, schema_info=schema_info)
    
    try:
        
        # if recall:
        res = sql_llm.invoke([AIMessage(content=gen_sql_prompt)])
        #     return res
        # res = SQLQuery(sql_query='SELECT cid, SUM(total_executed_volume_kg) AS total_executed_volume_kg\nFROM (\n    SELECT buyer_cid AS cid, SUM(executed_volume_kg) AS total_executed_volume_kg\n    FROM exchange_mart.fact_trade_individual_transactions\n    WHERE is_order_cancelled = false AND trade_is_rejected = false AND buyer_cid IS NOT NULL\n    GROUP BY buyer_cid\n    UNION ALL\n    SELECT seller_cid AS cid, SUM(executed_volume_kg) AS total_executed_volume_kg\n    FROM exchange_mart.fact_trade_individual_transactions\n    WHERE is_order_cancelled = false AND trade_is_rejected = false AND seller_cid IS NOT NULL\n    GROUP BY seller_cid\n) AS all_clients\nGROUP BY cid\nORDER BY total_executed_volume_kg DESC;')
        return res
    except Exception as e:
        if max_trials == 0:
            res = FailedSQLQuery(sql_query=recall_info.get("input", None), error=f"Failed to generate SQL query after 3 trials: {str(e)}")
        else:
            if isinstance(e, pydantic.ValidationError):
                err = e.errors()[0]
                res = generate_sql_query(args, max_trials - 1, True, {"msg": err['msg'], "input": err['input']})
            else:
                # {"msg": err['msg'], "input": err['input']}
                err = str(e)#.errors()[0]
                res = FailedSQLQuery(sql_query=err, error=str(e))
    return res
            
                

def execute_sql_query(sql_query: str):
    try:
        with engine.connect() as conn:
        # with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # cur.execute(sql_query)
            # result = cur.fetchall()
            df = pd.read_sql_query(sql_query, conn)
            return df #pd.DataFrame(result).replace({None: np.nan}) #.to_dict(orient="records")
        
    except (OperationalError, ProgrammingError, DatabaseError) as e:
        print(f"Error executing SQL query: {e}")
        return {'error': str(e)}



# Define Nodes
def ActionNode(state: State):
    """Retrieves the relevant schemas and tables"""
    msg = state["messages"][-1]
    outbound_msgs = []
    sql_query = ""
    print("ToolNode reached")
    command = Control.AGENT
    if hasattr(msg, "tool_calls"):
        print("tool call identified")
        for tool_call in msg.tool_calls:
            try:
                if tool_call['name'] == "schema_retreiver":
                    query = tool_call['args']['query']
                    # result = schema_retriever_tool.invoke(query)
                    rel_docs = schema_retriever.invoke(query)
                    schema_defs = [doc.page_content for doc in rel_docs]
                    result = '\n\n---\n'.join([json.dumps(doc.metadata, indent=2) for doc in rel_docs])
                    print(type(result))
                    response = ToolMessage(content=result,
                                        tool_call_id=tool_call["id"],
                                            name=tool_call["name"])
                    state["schema_info"] = schema_defs
                    outbound_msgs.append(response)
                elif tool_call['name'] == "SQLWriteQuery":
                    print("SQLWriteQuery tool call identified")
                    user_query = tool_call['args']['user_query']
                    query_plan = tool_call['args']['query_plan']
                    user_query = f"{user_query}\nBelow is a suggested qery plan: {query_plan}"
                    schema_info = state.get("schema_info")
                    res = generate_sql_query({"user_query": user_query, "schema_info": schema_info})
                    # gen_sql_prompt = sql_prompt.content.format(query=user_query, schema_info=schema_info)
                    # res = sql_llm.invoke([AIMessage(content=gen_sql_prompt)])
                    print(res)
                    if isinstance(res, FailedSQLQuery):
                        response = ToolMessage(content=res.error,
                                            tool_call_id=tool_call["id"],
                                                name=tool_call["name"])
                        outbound_msgs.append(response)
                        continue
                    response = ToolMessage(content=res.sql_query,
                                        tool_call_id=tool_call["id"],
                                            name=tool_call["name"])
                    sql_query = res.sql_query
                    command = Control.TOOL
                    outbound_msgs.append(response)
                    
            except Exception as e:
                # Always return a ToolMessage on error
                response = ToolMessage(
                    content=f"Tool execution failed: {str(e)}",
                    tool_call_id=tool_call["id"],
                    name=tool_call["name"]
                )
                outbound_msgs.append(response)
                
    return state | {"messages": outbound_msgs, "sql_query": sql_query, "command": command}

def human(state: State):
    """Reach out to human for feedback"""
    approval_msg = AIMessage(content="Approve agent to execute the below query")
    user_input = input("User: ")
    if user_input.lower() in {"q", "quit", "exit", "goodbye", "no", "stop", "terminate", 'never'}:
        return '__end__' 
    state["messages"].append(approval_msg)
    state["messages"].append(HumanMessage(content=user_input))
    return state | {"command": Control.AGENT}

def QueryExecutor(state: State):
    """Executes the SQL query"""
    print("QueryExecutor reached")
    sql_query = state["sql_query"]
    result = execute_sql_query(sql_query)
    command = Control.AGENT
    if isinstance(result, dict):
        return state | {"result": None, "messages": [AIMessage(content=f"Error executing SQL query: {result['error']}")], "command": command}
    else:
        df_meta = describe_dataframe(result)
        message = AIMessage(content=f"Here is the result of your query: {df_meta}")
    command = Control.AGENT
    return state | {"result": result.to_dict(orient="records"), "messages": [message], "command": command}






























from langgraph.checkpoint.sqlite import SqliteSaver
from pathlib import Path
import sqlite3

def get_sqlite_conn(path=None):
    if path is None:
        try:
            root_path = Path(__file__).parent.parent
        except Exception as e:
            root_path = Path(os.getcwd())
        path = root_path / "memory" / "agent.db"
        path.parent.mkdir(parents=True, exist_ok=True)
        conn = sqlite3.connect(path, check_same_thread=False)
        return conn
    else:
        return sqlite3.connect(path, check_same_thread=False)

memconn = get_sqlite_conn()
checkpointer = SqliteSaver(memconn)
        

flow = StateGraph(State)
flow.add_node("agent", agent)
flow.add_node('action_node', ActionNode)
flow.add_node('query_executor', QueryExecutor)
flow.add_node('human', human)

flow.set_entry_point("agent")
flow.add_conditional_edges('agent', router, {"action": "action_node", "__end__": "__end__"})
flow.add_conditional_edges('action_node', route_from_action, {"agent": "agent", "human": "human", "query_executor": "query_executor"})
# flow.add_edge("action_node", "agent")
flow.add_edge("query_executor", "agent")
flow.set_finish_point("agent")
model = flow.compile(checkpointer=checkpointer)

model

# conn = connect_db()
config = {"configurable": {"thread_id": "x010"}}

messages = model.invoke({"messages": [HumanMessage(content="")]},
                        config = config)

print("SELECT\n  EXTRACT(YEAR FROM t.execution_created_at) AS year,\n  CAST(\n    COUNT(DISTINCT CASE WHEN t.seller_cid IS NOT NULL AND ec.user_account_type = 'Individual' THEN t.execution_id END) +\n    COUNT(DISTINCT CASE WHEN t.buyer_cid IS NOT NULL AND ec2.user_account_type = 'Individual' THEN t.execution_id END)\n    AS numeric\n  ) AS total_transactions\nFROM exchange_mart.fact_trade_individual_transactions t\nLEFT JOIN exchange_mart.dim_client ec ON t.seller_cid = ec.cid\nLEFT JOIN exchange_mart.dim_client ec2 ON t.buyer_cid = ec2.cid\nWHERE EXTRACT(YEAR FROM t.execution_created_at) IN (2022, 2023)\nGROUP BY year\nORDER BY year;"
)

snapshot = model.get_state(config)

snapshot.values



messages = model.invoke({"messages": [HumanMessage(content="What is the total volume of transactions by individual clients?")]},
                        config = config)









snapshot = model.get_state(config)

snapshot.values

