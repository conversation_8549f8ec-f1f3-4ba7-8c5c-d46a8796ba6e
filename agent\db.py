import os
from psycopg2 import connect
from sqlalchemy.exc import (
    NoSuchTableError, NoSuchColumnError, 
    ProgrammingError, DataError, 
    InterfaceError, OperationalError, 
    DatabaseError
)
from agent.config import settings
from contextlib import contextmanager
from sqlalchemy import create_engine, text

from pathlib import Path
import sqlite3

def get_sqlite_conn(path=None, check_same_thread=False):
    if path is None:
        try:
            root_path = Path(__file__).parent.parent
        except Exception as e:
            root_path = Path(os.getcwd())
        path = root_path / "memory" / "agent.db"
        path.parent.mkdir(parents=True, exist_ok=True)
        conn = sqlite3.connect(path, check_same_thread=check_same_thread)
        return conn
    else:
        return sqlite3.connect(path, check_same_thread=check_same_thread)




def connect_db():
    engine = create_engine(f"postgresql://{settings.POSTGRES_USERNAME}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}")
    return engine.connect()
# conn = connect_db()

@contextmanager
def open_db_connection():
    try:
        conn = connect_db()
        yield conn
    finally:
        conn.close()


def get_engine():
    engine = create_engine(f"postgresql://{settings.POSTGRES_USERNAME}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DATABASE}")
    return engine

def validate_sql_query(query: str) -> dict:
    """
    Validates a SQL query for syntax and reference correctness using EXPLAIN.
    Assumes a global `conn` exists.

    Returns:
        dict with keys:
            - 'valid' (bool): True if query is valid
            - 'error' (str or None): Error message if invalid
            - 'type' (str or None): Type of error ('query', 'connection', etc.)
    """
    engine = get_engine()
    with  engine.connect() as conn:
        try:
            
            conn.execute(text(f"EXPLAIN {query}"))
            return {'valid': True, 'error': None, 'type': None}

        # Query-level errors (these are expected during validation)

        except Exception as e:
            if isinstance(e, (NoSuchTableError, NoSuchColumnError, 
                ProgrammingError, DataError)):

                return {'valid': False, 'error': str(e).strip(), 'type': 'query'}
            
            
            elif isinstance(e, InterfaceError):
                return {'valid': False, 'error': str(e).strip(), 'type': 'connection'}
            
            # Connection-level errors (shouldn't be handled silently)
            elif isinstance(e, (OperationalError, DatabaseError)):

                return {'valid': False, 'error': str(e).strip(), 'type': 'connection'}
            else:
                
                return {'valid': False, 'error': str(e).strip(), 'type': 'unknown'}

