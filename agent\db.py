from psycopg2 import connect
from psycopg2.errors import (
    UndefinedTable,
    UndefinedColumn,
    SyntaxError,
    InvalidTextRepresentation,
    DatatypeMismatch,
    UndefinedFunction,
    DatabaseError,
    OperationalError,
    InterfaceError,
)
from agent.config import settings
from contextlib import contextmanager


def connect_db():
    return connect(
        host=settings.POSTGRES_HOST,
        port=settings.POSTGRES_PORT,
        database=settings.POSTGRES_DATABASE,
        user=settings.POSTGRES_USERNAME,
        password=settings.POSTGRES_PASSWORD,
    )


@contextmanager
def open_db_connection():
    try:
        conn = connect_db()
        yield conn
    finally:
        conn.close()


def validate_sql_query(query: str) -> dict:
    """
    Validates a SQL query for syntax and reference correctness using EXPLAIN.
    Assumes a global `conn` exists.

    Returns:
        dict with keys:
            - 'valid' (bool): True if query is valid
            - 'error' (str or None): Error message if invalid
            - 'type' (str or None): Type of error ('query', 'connection', etc.)
    """
    with open_db_connection() as conn:
        try:
            cur = conn.cursor()
            cur.execute(f"EXPLAIN {query}")
            return {'valid': True, 'error': None, 'type': None}

        # Query-level errors (these are expected during validation)

        except Exception as e:
            if isinstance(e, (UndefinedTable, UndefinedColumn, SyntaxError, 
                InvalidTextRepresentation, DatatypeMismatch, UndefinedFunction)):

                conn.rollback()
                cur.close()
                return {'valid': False, 'error': str(e).strip(), 'type': 'query'}
            
            
            elif isinstance(e, InterfaceError):
                return {'valid': False, 'error': str(e).strip(), 'type': 'connection'}
            
            # Connection-level errors (shouldn't be handled silently)
            elif isinstance(e, (OperationalError, DatabaseError)):
                conn.rollback()
                cur.close()
                return {'valid': False, 'error': str(e).strip(), 'type': 'connection'}
            else:
                conn.rollback()
                cur.close()
                return {'valid': False, 'error': str(e).strip(), 'type': 'unknown'}

