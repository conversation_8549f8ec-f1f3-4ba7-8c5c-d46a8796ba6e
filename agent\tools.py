from langchain.tools.retriever import create_retriever_tool
from psycopg2.extras import RealDictCursor
from sqlalchemy.exc import OperationalError, ProgrammingError, DatabaseError
import pandas as pd
from agent.schema import load_schema_docs
from agent.vectorstore import create_vector_store
from agent.config import settings
from agent.db import connect_db, open_db_connection, get_engine, get_sqlite_conn
from langchain_qdrant import QdrantVectorStore



def get_schema_retriever(collection_name: str, embeddings, **kwargs):
    """
    Connect to an existing Qdrant collection and return a retriever
    for use in LLM-based RAG pipelines.
    """
    vs = QdrantVectorStore.from_existing_collection(
        embedding=embeddings,
        collection_name=collection_name,
        url=settings.QDRANT_URL, api_key=settings.QDRANT_API_KEY,
    )
    retriever = vs.as_retriever(**kwargs)
    return retriever


def get_schema_retriever_tool(schema_retreiver):
    # schema_retreiver = vector_store.as_retriever(top_k=3)
    return create_retriever_tool(
        schema_retreiver,
        "schema_retreiver",
        "Search for relevant schemas and tables. This uses semantic search to find the most relevant schemas and tables"
    )

def get_schema_memory_retriever(vector_store):
    return vector_store.as_retriever(top_k=3)

def execute_sql_query(sql_query: str):
    with open_db_connection() as conn:
        try:
            result = pd.read_sql_query(sql_query, conn)
            # with conn.cursor(cursor_factory=RealDictCursor) as cur:
            #     cur.execute(sql_query)
            #     result = cur.fetchall()
            #     return pd.DataFrame(result).to_dict(orient="records")
            return result.to_dict(orient="records")
        except (OperationalError, ProgrammingError, DatabaseError) as e:
            print(f"Error executing SQL query: {e}")
            return {'error': str(e)}

# Removed module-level initialization to prevent circular imports
# These will be initialized lazily when needed

def get_default_vector_store():
    """Lazy initialization of vector store"""
    return create_vector_store(load_schema_docs(settings.SCHEMA_DEFINIOTIONS_PATH))


def get_default_schema_retriever():
    """Lazy initialization of schema retriever"""
    return get_schema_memory_retriever(get_default_vector_store())

def get_default_schema_retriever_tool():
    """Lazy initialization of schema retriever tool"""
    return get_schema_retriever_tool(get_schema_memory_retriever(get_default_vector_store()))

