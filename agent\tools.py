from langchain.tools.retriever import create_retriever_tool
from psycopg2.extras import RealDictCursor
from psycopg2.errors import OperationalError, ProgrammingError, DatabaseError
import pandas as pd
from agent.schema import load_schema_docs
from agent.vectorstore import create_vector_store
from agent.config import settings
from agent.db import connect_db, open_db_connection



def get_schema_retriever_tool(vector_store):
    schema_retreiver = vector_store.as_retriever(top_k=3)
    return create_retriever_tool(
        schema_retreiver,
        "schema_retreiver",
        "Search for relevant schemas and tables. This uses semantic search to find the most relevant schemas and tables"
    )

def execute_sql_query(sql_query: str):
    with open_db_connection() as conn:
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(sql_query)
                result = cur.fetchall()
                return pd.DataFrame(result).to_dict(orient="records")
            
        except (OperationalError, ProgrammingError, DatabaseError) as e:
            print(f"Error executing SQL query: {e}")
            return {'error': str(e)}

vector_store = create_vector_store(load_schema_docs(settings.SCHEMA_DEFINIOTIONS_PATH))   # This will need to be replaced with path to supabase
schema_retriever_tool = get_schema_retriever_tool(vector_store)