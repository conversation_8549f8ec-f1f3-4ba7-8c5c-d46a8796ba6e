from langgraph.graph import StateGraph
from agent.tools import get_schema_retriever_tool
from langchain_core.messages import HumanMessage
from agent.models import AgentState
from agent.config import LLMs, settings
from agent.db import connect_db


from agent.nodes import ActionNode, QueryExecutor, human, router, route_from_action, agent


class Agent:
    def __init__(self, vector_store):
        self.vector_store = vector_store
        self.conn = connect_db()
        self.workflow = None
        self.sql_llm = LLMs.sql_llm
    
    
    def build_workflow(self, rebuild: bool = False):
        if rebuild:
            self.workflow = None
            
        if self.workflow:
            return self.workflow
       
        flow = StateGraph(AgentState)
        flow.add_node("agent", agent)
        
        flow.add_node("agent", agent)
        flow.add_node('action_node', ActionNode)
        flow.add_node('query_executor', QueryExecutor)
        flow.add_node('human', human)
        flow.set_entry_point("agent")
        flow.add_conditional_edges('agent', router, {"action": "action_node", "__end__": "__end__"})
        flow.add_conditional_edges('action_node', route_from_action, {"agent": "agent", "human": "human", "query_executor": "query_executor"})
        flow.add_edge("query_executor", "agent")
        flow.set_finish_point("agent")
        self.workflow = flow.compile()
        return self.workflow
    
    def run(self, user_msg: str):
        if not self.workflow:
            raise Exception("Workflow not built")
        return self.workflow.invoke({"messages": [HumanMessage(content=user_msg)]}
        )
    
    def chat(self, user_msg: str):
        if not self.workflow:
            raise Exception("Workflow not built")
        config = {"key":123}
        message = {"messages": [HumanMessage(content=user_msg)]}
        response = self.workflow.invoke(message)
        data = response['result']
        ai_response = response['messages'][-1].content
        sql_query = response['sql_query']

        return {"response": ai_response, "data":data, "sql_query": sql_query}
    
            
        
