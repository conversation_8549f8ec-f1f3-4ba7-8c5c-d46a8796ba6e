from langgraph.graph import StateGraph
from agent.tools import get_schema_retriever_tool
from langchain_core.messages import HumanMessage
from agent.models import AgentState
from agent.config import LLMs, settings
from agent.db import connect_db, get_sqlite_conn, get_engine
from langgraph.checkpoint.sqlite import SqliteSaver
from agent.logger import get_logger, log_agent_step, log_database_operation

from agent.nodes import ActionNode, QueryExecutor, human, router, route_from_action, agent

# Get logger for this module
logger = get_logger(__name__)


class Agent:
    def __init__(self):
        logger.info("🚀 Initializing Sherlock Agent")

        # Initialize database connections with logging
        logger.info("📊 Connecting to PostgreSQL database...")
        self.conn = connect_db()

        logger.info("🧠 Initializing SQL LLM...")
        self.sql_llm = LLMs.get_sql_llm()

        logger.info("💾 Connecting to SQLite memory database...")
        self.memconn = get_sqlite_conn()

        self.workflow = None
        logger.info("✅ Agent initialization complete")
    
    
    def build_workflow(self, rebuild: bool = False):
        if rebuild:
            self.workflow = None
            
        if self.workflow:
            return self.workflow
       
        flow = StateGraph(AgentState)
        
        flow.add_node("agent", agent)
        flow.add_node('action_node', ActionNode)
        flow.add_node('query_executor', QueryExecutor)
        flow.add_node('human', human)
        
        flow.set_entry_point("agent")
        flow.add_conditional_edges('agent', router, {"action": "action_node", "__end__": "__end__"})
        flow.add_conditional_edges('action_node', route_from_action, {"agent": "agent", "human": "human", "query_executor": "query_executor"})
        flow.add_edge("query_executor", "agent")
        flow.set_finish_point("agent")

        checkpointer = SqliteSaver(self.memconn)
        self.workflow = flow.compile(checkpointer=checkpointer)
        return self.workflow
    
    def run(self, user_msg: str):
        if not self.workflow:
            raise Exception("Workflow not built")
        
        
        return self.workflow.invoke({"messages": [HumanMessage(content=user_msg)]}
        )
    
    def chat(self, user_msg: str, session_id: str):
        if not self.workflow:
            raise Exception("Workflow not built")

        logger.info(f"💬 User message: {user_msg}")
        log_agent_step("chat_start", {"user_message": user_msg})
        config = {"configurable": {"thread_id": session_id}}
        message = {"messages": [HumanMessage(content=user_msg)]}

        try:
            response = self.workflow.invoke(message, config=config)
            data = response['result']
            ai_response = response['messages'][-1].content
            sql_query = response['sql_query']

            logger.info(f"🤖 Agent response: {ai_response[:100]}...")
            log_agent_step("chat_complete", {
                "user_message": user_msg,
                "response_preview": ai_response[:100],
                "has_data": bool(data),
                "sql_query": sql_query[:100] if sql_query else None
            })

            return {"response": ai_response, "data": data, "sql_query": sql_query}

        except Exception as e:
            logger.error(f"❌ Error in chat workflow: {str(e)}")
            log_agent_step("chat_error", {"user_message": user_msg, "error": str(e)})
            raise
    
            
        
