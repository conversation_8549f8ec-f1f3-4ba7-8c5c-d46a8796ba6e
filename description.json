[{"schema": "trade_mart", "tables": {"fact_warehouse_dailyinventorybalance": " it captures the status of items under lien and provides flags for deleted records. The table also specifies the type of inventory transaction, such as debits, credits, and liens, offering a robust historical log for inventory management\nThis table is associated with the following: - **Inventory Position**: The total unliened volume of commodity available at each warehouse at the end of the specified period.\n", "dim_crop": " This table is crucial for tracking crop-related activities and identifying the crops associated with specific farmers and warehouses.", "dim_item": "This table plays a crucial role in managing and categorizing items within the warehouse management and loan processing systems.", "dim_client": "The `wb_dim_client` table contains personal details and contact information for clients on the Workbench platform. This table is essential for maintaining a comprehensive profile of each client, ensuring accurate and up-to-date personal information for operational and communication purposes.\nThis table is associated with the following: - **AFTL Clients**: The distinct count of clients registered on the workbench platform.\n", "dim_clientbank": "wb_dim_clientbank` table contains detailed bank information for clients on the Workbench platform.This table is essential for managing and verifying the bank accounts of clients, ensuring accurate and secure financial transactions.", "fact_loan": "The `wb_fact_loan` table stores records of loans issued to clients (Farmers).  This table plays a crucial role in monitoring the lifecycle and status of loans, ensuring precise tracking and management of financial obligations and repayments.\nThis table is associated with the following: - **Active Farmers**: Registered AFTL farmers on workbench who have engaged in GRN or loan transactions during the specified period, this could be year or season.\n - **Hectares for Input Loan **: The total area of land, in hectares, financed for each farmer's fair trade loans that are fully approved and not reverted.\n - **Total Amount Repaid**: The total monetary value repaid on loans.\n - **Total Loan Value**: The total monetary value of loans disbursed.\n - **Total Repayment Value**: The total monetary value of loans disbursed plus interest.\n - **Total AFTL Loan Transactions**: The distinct count of unique workbench LOAN IDs.\n", "fact_loan_breakdown": "The table includes critical financial details such as the total and unit prices, total loan value, repayment value, amounts repaid, insurance, credit risk guarantee (CRG), interest, administrative fees, equity, and remaining balance. It also records the loan status and various verification statuses. Flags indicate if the loan breakdown is repaid, approved, approval completed, rejected, or reverted. This table is vital for a comprehensive understanding and management of loan disbursements and repayments.\nThis table is associated with the following: - **Total Amount Repaid**: The total monetary value repaid on loans.\n - **Total Loan Value**: The total monetary value of loans disbursed.\n - **Total Repayment Value**: The total monetary value of loans disbursed plus interest.\n", "dim_warehouse": "This table is essential for managing and tracking warehouse data and their geographical distribution, and it can be used to determine the location of associated farmers based on the warehouse they belong to.", "fact_grn": "The table 'wb_fact_grn' catalogs detailed records of Goods Receipt Note (GRN) transactions, encompassing various aspects of goods reception and transactional details. Each GRN record is uniquely identified by 'wb_fact_grn.grn_id' and includes timestamps for creation ('wb_fact_grn.created') and updates ('wb_fact_grn.updated'), providing a timeline of GRN processing. Key attributes such as 'wb_fact_grn.bags' (number of bags), 'wb_fact_grn.net_weight' (net weight), and 'wb_fact_grn.moisture' (moisture percentage) detail physical characteristics of received goods. Financial aspects like 'wb_fact_grn.total_commodity_price' and 'wb_fact_grn.transaction_fees' offer insights into transactional costs associated with each GRN. Operational flags such as 'wb_fact_grn.is_approved,' 'wb_fact_grn.is_processed,' and 'wb_fact_grn.is_traded' provide operational status indicators, facilitating efficient management of inventory and transaction processing.\nThis table can be used to track things like the following: - **Active AFTL Clients**: Registered AFTL clients on workbench who have engaged in GRN transactions during the specified period, this could be year or season, etc.\n - **Active Farmers**: Registered AFTL farmers on workbench who have engaged in GRN or loan transactions during the specified period, this could be year or season.\n - **Aggregation Cost**: This is a direct cost which is the transaction value paid to farmers and clients for GRN transactions, not accounting for logistics cost.\n - **Total GRN Transactions**: The distinct count of unique GRN IDs.\n - **Volume Aggregated **: The total weight of all approved GRN transactions on WorkBench related to loan repayment, storage, storage to trade, trade, and broker payment, excluding reverted transactions.\n", "dim_farmer": "This table contains information about FEOs (Field Extension Officers)\nThis table is associated with the following: - **Number of Farmers Reached **: The distinct count of registered farmers."}}, {"schema": "exchange_mart", "tables": {"fact_clientwalletlog": "The table tracks the lien amounts and total wallet amounts before and after transactions, along with the available balance changes. It flags entries as deleted when necessary and specifies the type of wallet transaction, such as credits, debits, liens, and adjustments. This table is essential for auditing and monitoring the financial activities within client wallets over time.", "fact_clientwallet": "This table is crucial for real-time monitoring and management of client wallet positions.\nThis table is used to track the following: - **Client Wallet Balance**: The total unliened amount available in each client's wallet at the end of the specified period.\n", "dim_client": "This table is essential for managing client information and ensuring compliance with regulatory requirements.\nThis table is associated with the following: - **Exchange Clients**: The distinct count of clients registered on the africa exchange platform.\n", "fact_matchedorder": "captures detailed records of trade transactions, encompassing the volume and value of both buy and sell activities since inception. Each record is timestamped with creation ('tr_fact_matchedorder.created') and last update ('tr_fact_matchedorder.updated') times, providing insights into transaction timing. Key identifiers such as 'tr_fact_matchedorder.tid' and 'tr_fact_matchedorder.matched_id' distinguish individual trade and matched order IDs, crucial for tracking transaction history. The table includes essential transaction specifics such as buyer and seller details ('tr_fact_matchedorder.buyer_cid,' 'tr_fact_matchedorder.seller_cid'), alongside order characteristics like units ('tr_fact_matchedorder.order_units'), unit prices ('tr_fact_matchedorder.order_price'), and matched volumes ('tr_fact_matchedorder.matched_units'). Financial elements such as fees ('tr_fact_matchedorder.exchange_fee' 'tr_fact_matchedorder.brokerage_fee') and VAT ('tr_fact_matchedorder.vat_value') complement transactional data, ensuring comprehensive financial analysis. Overall, this table serves as a vital repository for analyzing trade performance and financial metrics within the specified trading environment.\nThis table is associated with the following: - **FI Sales Value**: The total transaction value of 'Buy' Matched Fixed Income units sold by Exchange Issuance.\n - **FI Units Issued**: The total units of 'Sell' Fixed Income orders introduced to the exchange by Exchange Issuance.\n - **FI Units Sold**: The total units of 'Buy' Matched Fixed Income units bought, which were sold by Exchange Issuance.\n - **Matched Volume **: The product of matched units and volume per unit, considering the unit conversion for different types of securities.\n - **Volume Sold **: The sum of volume of commodities (OTC, Dawa, and Spot) sold on the exchange by AFTL NG and Physical Market's Clients, for Buy orders only.\n - **Weighted Aggregation Cost**: The calculated average cost of purchased commodities based on the total volume purchased.\n", "fact_transactions": "serves as a comprehensive repository for transaction details within the AFEX trading system. It meticulously captures various aspects of each transaction, including financial specifics, order details, trade identifiers, and client information. Each transaction record includes essential fields such as actual trade values and volumes, order IDs, creation timestamps, and unique identifiers for different entities involved in the transaction process. Notably, it tracks trade statuses, validation statuses within external systems like OVS, and summarizes trade status updates. Additional attributes cover transaction types, flags indicating trade status (e.g., rejected, deleted), and detailed metrics such as trade volumes in both metric tons and kilograms. Financial components are extensively logged, including order prices, fees, discounts, and taxes, with breakdowns provided for each transaction's fee structure. Moreover, the table captures client-specific information, including client identifiers, account types, contact details, and KYC completion statuses. This detailed transactional data is crucial for auditing, performance analysis, and regulatory compliance within the AFEX trading environment, offering comprehensive insights into trade activities and client interactions.\nThis table is associated with the following: - **Active Exchange Clients**: Clients registered on the exchnage who have engaged in buy or sell transactions on the exchange, and or wallet withdrawal or deposit, and or cia to csd conversion transactions during the specified period, this could be year or season, etc.\n - **Delivered Volume**: The weight of OTC security logged by the logistics officer as delivered, measured in metric tons or 100 kilograms. trade_mart.fact_trade_individual_transactions where security_type = 'OTC' and execution_date is not null\n - **Market Turnover **: The sum of the product of price and quantity for all non-OTC buy transactions, excluding transactions involving exchange issuance. Assumes all exchange issuance transactions equate to FI issuance and buy backs.\n - **Total Trade Transactions**: The distinct count of unique buyer's MATCHED IDs.\n - **Transaction Value / Order Price**: The product of price and quantity for each transaction.\n - **Transaction Value with Fees / Order Price with Fees**: The total transaction value including all associated fees.\n - **Volume Sold **: The sum of volume of commodities (OTC, Dawa, and Spot) sold on the exchange by AFTL NG and Physical Market's Clients, for Buy orders only.\n - **Weighted Aggregation Cost**: The calculated average cost of purchased commodities based on the total volume purchased.\n", "fact_trade_individual_transactions": " The table description is: The table `tr_fact_trade_individual_transactions` captures detailed records of individual trade transactions, including execution details, trade statuses, and associated fees. It encompasses various attributes related to trades, contracts, and dispatches, reflecting the comprehensive lifecycle of a trade from execution to delivery, while also tracking logistical information and any relevant operational updates. The table supports analysis of trading activities and performance metrics within the trading system. \nThis table is associated with the following: - **Active Exchange Clients**: Clients registered on the exchnage who have engaged in buy or sell transactions on the exchange, and or wallet withdrawal or deposit, and or cia to csd conversion transactions during the specified period, this could be year or season, etc.\n - **Delivered Volume**: The weight of OTC security logged by the logistics officer as delivered, measured in metric tons or 100 kilograms. trade_mart.fact_trade_individual_transactions where security_type = 'OTC' and execution_date is not null\n - **Market Turnover **: The sum of the product of price and quantity for all non-OTC buy transactions, excluding transactions involving exchange issuance. Assumes all exchange issuance transactions equate to FI issuance and buy backs.\n - **Total Trade Transactions**: The distinct count of unique buyer's MATCHED IDs.\n - **Transaction Value / Order Price**: The product of price and quantity for each transaction.\n - **Transaction Value with Fees / Order Price with Fees**: The total transaction value including all associated fees.\n - **Volume Sold **: The sum of volume of commodities (OTC, Dawa, and Spot) sold on the exchange by AFTL NG and Physical Market's Clients, for Buy orders only.\n - **Weighted Aggregation Cost**: The calculated average cost of purchased commodities based on the total volume purchased.", "fact_closingprice": "serves as a comprehensive repository for daily price data across various securities. It captures essential details such as unique identifiers for each record, the date and time of price calculation, and the type of board associated with each security, which can range from 'Virtual' to 'OTC'. Each record also includes specifics of the security itself, including its code, name, and type, which categorizes securities into types like 'Virtual', 'SCIT', 'FI', and others. Financial metrics such as daily closing values, total daily matched units, and their equivalents in kilograms are meticulously logged, along with price ranges for the current and previous days, opening and closing prices per unit and in kilograms, and percentage changes from previous closing prices. This structured data facilitates detailed analysis of daily trading patterns, price movements, and performance metrics crucial for understanding the dynamics of the AFEX trading ecosystem. Overall, the `tr_fact_closingprice` table plays a pivotal role in monitoring and analyzing the daily performance of securities within AFEX. It consolidates a wealth of data points essential for tracking market trends, evaluating price volatility, and assessing the overall health of various securities. By documenting not only closing prices but also associated metrics like trading volumes, price ranges, and percentage changes, this table supports informed decision-making and strategic planning for investors, traders, and analysts operating within AFEX's trading environment.", "fact_prices_securities_prices": "This data is useful for analyzing the trends and performance of different securities over time, as well as for monitoring changes in prices and trading patterns. It can also be used for forecasting future prices and making informed investment decisions.", "fact_prices_commodities_prices": " These values are important for analyzing trends and making informed decisions in commodity trading.", "fact_prices_aei": "These data points are essential for traders and investors to track the performance of commodities on the Africa Exchange.", "dim_security": "The table is designed to assist users in identifying and organizing various securities traded on AFEX, including cash crops (through Africa Commodity Exchange Limited), investments (through Africa Investment Limited), and fair trade products (through Africa Fair Trade Limited). This table is associated with the following: - **Securities Listed**: The distinct count of securities sold on the exchange during the specified period.\n"}}]