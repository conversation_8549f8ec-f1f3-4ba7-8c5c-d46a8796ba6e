{"timestamp": "2025-07-11T11:12:42.271265Z", "level": "ERROR", "logger": "agent.database", "message": "Database operation failed: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 192, "operation": "postgres_connect", "success": false, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 21.14262866973877, "error": "(psycopg2.OperationalError) connection to server at \"********\", port 5432 failed: Connection timed out (0x0000274C/10060)\n\tIs the server running on that host and accepting TCP/IP connections?\n\n(Background on this error at: https://sqlalche.me/e/20/e3q8)"}
{"timestamp": "2025-07-11T11:12:42.291226Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in connect_db: (psycopg2.OperationalError) connection to server at \"********\", port 5432 failed: Connection timed out (0x0000274C/10060)\n\tIs the server running on that host and accepting TCP/IP connections?\n\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 145, in __init__\n    self._dbapi_connection = engine.raw_connection()\n                             ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 3288, in raw_connection\n    return self.pool.connect()\n           ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 452, in connect\n    return _ConnectionFairy._checkout(self)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 1267, in _checkout\n    fairy = _ConnectionRecord.checkout(pool)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 716, in checkout\n    rec = pool._do_get()\n          ^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py\", line 169, in _do_get\n    with util.safe_reraise():\n         ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py\", line 147, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py\", line 167, in _do_get\n    return self._create_connection()\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 393, in _create_connection\n    return _ConnectionRecord(self)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 678, in __init__\n    self.__connect()\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 902, in __connect\n    with util.safe_reraise():\n         ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py\", line 147, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 898, in __connect\n    self.dbapi_connection = connection = pool._invoke_creator(self)\n                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py\", line 637, in connect\n    return dialect.connect(*cargs, **cparams)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 615, in connect\n    return self.loaded_dbapi.connect(*cargs, **cparams)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\psycopg2\\__init__.py\", line 122, in connect\n    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\npsycopg2.OperationalError: connection to server at \"********\", port 5432 failed: Connection timed out (0x0000274C/10060)\n\tIs the server running on that host and accepting TCP/IP connections?\n\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\development\\sherlock_agent\\agent\\db.py\", line 52, in connect_db\n    conn = engine.connect()\n           ^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 3264, in connect\n    return self._connection_cls(self)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 147, in __init__\n    Connection._handle_dbapi_exception_noconnection(\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 2426, in _handle_dbapi_exception_noconnection\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 145, in __init__\n    self._dbapi_connection = engine.raw_connection()\n                             ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\base.py\", line 3288, in raw_connection\n    return self.pool.connect()\n           ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 452, in connect\n    return _ConnectionFairy._checkout(self)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 1267, in _checkout\n    fairy = _ConnectionRecord.checkout(pool)\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 716, in checkout\n    rec = pool._do_get()\n          ^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py\", line 169, in _do_get\n    with util.safe_reraise():\n         ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py\", line 147, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\impl.py\", line 167, in _do_get\n    return self._create_connection()\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 393, in _create_connection\n    return _ConnectionRecord(self)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 678, in __init__\n    self.__connect()\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 902, in __connect\n    with util.safe_reraise():\n         ^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py\", line 147, in __exit__\n    raise exc_value.with_traceback(exc_tb)\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\pool\\base.py\", line 898, in __connect\n    self.dbapi_connection = connection = pool._invoke_creator(self)\n                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\create.py\", line 637, in connect\n    return dialect.connect(*cargs, **cparams)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\sqlalchemy\\engine\\default.py\", line 615, in connect\n    return self.loaded_dbapi.connect(*cargs, **cparams)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\psycopg2\\__init__.py\", line 122, in connect\n    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nsqlalchemy.exc.OperationalError: (psycopg2.OperationalError) connection to server at \"********\", port 5432 failed: Connection timed out (0x0000274C/10060)\n\tIs the server running on that host and accepting TCP/IP connections?\n\n(Background on this error at: https://sqlalche.me/e/20/e3q8)", "error_type": "OperationalError", "context": "connect_db"}
{"timestamp": "2025-07-11T11:22:11.069502Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    response = model_with_tools.invoke(messages)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 5}}
{"timestamp": "2025-07-11T11:26:58.958694Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 108}
{"timestamp": "2025-07-11T11:26:58.984754Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    response = model_with_tools.invoke(messages)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 5}}
{"timestamp": "2025-07-11T11:34:08.575752Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 108}
{"timestamp": "2025-07-11T11:34:08.584579Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    try:\n         \n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 6}}
{"timestamp": "2025-07-11T11:34:50.452437Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 108}
{"timestamp": "2025-07-11T11:34:50.464586Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    try:\n         \n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 5}}
{"timestamp": "2025-07-11T11:43:09.730883Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 108}
{"timestamp": "2025-07-11T11:43:09.745598Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    try:\n         \n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 5}}
{"timestamp": "2025-07-11T11:48:19.732485Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 108}
{"timestamp": "2025-07-11T11:48:19.741730Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    try:\n         \n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 6}}
{"timestamp": "2025-07-11T11:48:32.342015Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 108}
{"timestamp": "2025-07-11T11:48:32.364137Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 103, in agent\n    try:\n         \n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 5}}
{"timestamp": "2025-07-11T11:49:09.906496Z", "level": "ERROR", "logger": "agent.nodes", "message": "❌ Error in agent node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "nodes", "function": "agent", "line": 110}
{"timestamp": "2025-07-11T11:49:09.924673Z", "level": "ERROR", "logger": "agent.errors", "message": "Error in agent_node: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "module": "logger", "function": "log_error", "line": 241, "exception": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\development\\sherlock_agent\\agent\\nodes.py\", line 104, in agent\n    response = model_with_tools.invoke(messages)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\runnables\\base.py\", line 5431, in invoke\n    return self.bound.invoke(\n           ^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 372, in invoke\n    self.generate_prompt(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 957, in generate_prompt\n    return self.generate(prompt_messages, stop=stop, callbacks=callbacks, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 776, in generate\n    self._generate_with_cache(\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py\", line 1022, in _generate_with_cache\n    result = self._generate(\n             ^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py\", line 705, in _generate\n    response = self.client.create(**payload)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_utils\\_utils.py\", line 279, in wrapper\n    return func(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py\", line 914, in create\n    return self._post(\n           ^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1242, in post\n    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 919, in request\n    return self._request(\n           ^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\miniconda3\\envs\\shervenv\\Lib\\site-packages\\openai\\_base_client.py\", line 1023, in _request\n    raise self._make_status_error_from_response(err.response) from None\nopenai.BadRequestError: Error code: 400 - {'error': {'message': \"Invalid parameter: messages with role 'tool' must be a response to a preceeding message with 'tool_calls'.\", 'type': 'invalid_request_error', 'param': 'messages.[4].role', 'code': None}}", "error_type": "BadRequestError", "context": "agent_node", "state_summary": {"message_count": 6}}
