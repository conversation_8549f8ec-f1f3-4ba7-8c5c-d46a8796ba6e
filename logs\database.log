{"timestamp": "2025-07-11T11:12:21.041111Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:12:21.041111Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:12:42.271265Z", "level": "ERROR", "logger": "agent.database", "message": "Database operation failed: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 192, "operation": "postgres_connect", "success": false, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 21.14262866973877, "error": "(psycopg2.OperationalError) connection to server at \"********\", port 5432 failed: Connection timed out (0x0000274C/10060)\n\tIs the server running on that host and accepting TCP/IP connections?\n\n(Background on this error at: https://sqlalche.me/e/20/e3q8)"}
{"timestamp": "2025-07-11T11:13:59.431129Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:13:59.431129Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:14:01.295549Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 1.8322052955627441}
{"timestamp": "2025-07-11T11:14:01.317321Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: C:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:14:01.320862Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "C:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
{"timestamp": "2025-07-11T11:21:30.509610Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:21:30.509610Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:21:32.559427Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 2.040465831756592}
{"timestamp": "2025-07-11T11:21:32.572914Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: C:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:21:32.572914Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "C:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
{"timestamp": "2025-07-11T11:24:11.793054Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:24:11.793223Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:24:13.826116Z", "level": "INFO", "logger": "agent.db", "message": "✅ Successfully connected to PostgreSQL database in 2.03s", "module": "db", "function": "connect_db", "line": 55}
{"timestamp": "2025-07-11T11:24:13.830435Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 2.030099630355835}
{"timestamp": "2025-07-11T11:24:13.835501Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:24:13.844531Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
{"timestamp": "2025-07-11T11:42:49.333272Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:42:49.333272Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:42:52.377770Z", "level": "INFO", "logger": "agent.db", "message": "✅ Successfully connected to PostgreSQL database in 3.04s", "module": "db", "function": "connect_db", "line": 55}
{"timestamp": "2025-07-11T11:42:52.377770Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 3.043945074081421}
{"timestamp": "2025-07-11T11:42:52.389073Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:42:52.389073Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
{"timestamp": "2025-07-11T11:48:03.141700Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:48:03.141700Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:48:04.941984Z", "level": "INFO", "logger": "agent.db", "message": "✅ Successfully connected to PostgreSQL database in 1.81s", "module": "db", "function": "connect_db", "line": 55}
{"timestamp": "2025-07-11T11:48:04.941984Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 1.8101232051849365}
{"timestamp": "2025-07-11T11:48:04.956946Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:48:04.961664Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
{"timestamp": "2025-07-11T11:49:00.611737Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:49:00.611737Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:49:02.475112Z", "level": "INFO", "logger": "agent.db", "message": "✅ Successfully connected to PostgreSQL database in 1.86s", "module": "db", "function": "connect_db", "line": 55}
{"timestamp": "2025-07-11T11:49:02.475112Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 1.8633747100830078}
{"timestamp": "2025-07-11T11:49:02.481758Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:49:02.481758Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "c:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
{"timestamp": "2025-07-11T11:49:25.643661Z", "level": "DEBUG", "logger": "agent.db", "message": "Validating SQL query: SELECT COUNT(*) AS total_farmers FROM trade_mart.dim_farmer WHERE is_deleted IS FALSE OR is_deleted ...", "module": "db", "function": "validate_sql_query", "line": 129}
{"timestamp": "2025-07-11T11:49:25.643661Z", "level": "DEBUG", "logger": "agent.db", "message": "Creating SQLAlchemy engine", "module": "db", "function": "get_engine", "line": 106}
{"timestamp": "2025-07-11T11:49:25.643661Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: engine_create", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "engine_create", "success": true}
{"timestamp": "2025-07-11T11:49:28.161530Z", "level": "DEBUG", "logger": "agent.db", "message": "✅ SQL query validation successful in 2.518s", "module": "db", "function": "validate_sql_query", "line": 137}
{"timestamp": "2025-07-11T11:49:28.169757Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sql_validate", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sql_validate", "success": true, "validation_time": 2.517868757247925, "query": "SELECT COUNT(*) AS total_farmers FROM trade_mart.dim_farmer WHERE is_deleted IS FALSE OR is_deleted IS NULL;"}
{"timestamp": "2025-07-11T11:49:28.304504Z", "level": "DEBUG", "logger": "agent.db", "message": "Opening database connection context", "module": "db", "function": "open_db_connection", "line": 85}
{"timestamp": "2025-07-11T11:49:28.305506Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:49:28.306882Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:49:30.131571Z", "level": "INFO", "logger": "agent.db", "message": "✅ Successfully connected to PostgreSQL database in 1.83s", "module": "db", "function": "connect_db", "line": 55}
{"timestamp": "2025-07-11T11:49:30.131571Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 1.8260650634765625}
{"timestamp": "2025-07-11T11:49:30.527585Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sql_execute", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sql_execute", "success": true, "execution_time": 2.223501443862915, "row_count": 1, "query": "SELECT COUNT(*) AS total_farmers FROM trade_mart.dim_farmer WHERE is_deleted IS FALSE OR is_deleted IS NULL;"}
{"timestamp": "2025-07-11T11:49:30.655699Z", "level": "DEBUG", "logger": "agent.db", "message": "Database connection closed successfully", "module": "db", "function": "open_db_connection", "line": 96}
{"timestamp": "2025-07-11T11:49:30.655699Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_disconnect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_disconnect", "success": true}
{"timestamp": "2025-07-11T11:52:34.635050Z", "level": "INFO", "logger": "agent.db", "message": "Attempting to connect to PostgreSQL database", "module": "db", "function": "connect_db", "line": 47}
{"timestamp": "2025-07-11T11:52:34.636049Z", "level": "DEBUG", "logger": "agent.db", "message": "Connection string: ********************************************/dwhtest", "module": "db", "function": "connect_db", "line": 49}
{"timestamp": "2025-07-11T11:52:37.580899Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: postgres_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "postgres_connect", "success": true, "host": "********", "port": 5432, "database": "dwhtest", "connection_time": 2.935638427734375}
{"timestamp": "2025-07-11T11:52:37.580899Z", "level": "DEBUG", "logger": "agent.db", "message": "Connecting to SQLite database: C:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db", "module": "db", "function": "get_sqlite_conn", "line": 32}
{"timestamp": "2025-07-11T11:52:37.580899Z", "level": "INFO", "logger": "agent.database", "message": "Database operation successful: sqlite_connect", "module": "logger", "function": "log_database_operation", "line": 189, "operation": "sqlite_connect", "success": true, "database_path": "C:\\Users\\<USER>\\development\\sherlock_agent\\memory\\agent.db"}
