from agent.config import openai_api_key
from agent.workflow import Agent
from langchain_core.messages import HumanMessage


def main():
    agent = Agent()
    flow = agent.build_workflow()
    messages = [HumanMessage(content="What is the total number of farmers?")]
    # response = agent.run(messages[0].content)
    response = flow.invoke(messages)
    print(response)

if __name__ == "__main__":
    main()
