#!/usr/bin/env python3
"""
Streamlit UI for Sherlock Agent - AFEX's AI Data Analyst
A user-friendly interface for interacting with the Sherlock Agent system.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import json
import uuid
from pathlib import Path
import time

# Configure Streamlit page
st.set_page_config(
    page_title="Sherlock Agent - AFEX AI Data Analyst",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .agent-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .sql-code {
        background-color: #f5f5f5;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #4caf50;
        font-family: 'Courier New', monospace;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
def initialize_session_state():
    """Initialize Streamlit session state variables"""
    if 'sessions' not in st.session_state:
        st.session_state.sessions = {}
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = None
    if 'agent' not in st.session_state:
        st.session_state.agent = None
    if 'agent_initialized' not in st.session_state:
        st.session_state.agent_initialized = False

def create_new_session():
    """Create a new chat session"""
    session_id = str(uuid.uuid4())[:8]
    session_name = f"Session {len(st.session_state.sessions) + 1}"
    
    st.session_state.sessions[session_id] = {
        'name': session_name,
        'created_at': datetime.now(),
        'messages': [],
        'queries_executed': 0,
        'total_rows_returned': 0
    }
    st.session_state.current_session_id = session_id
    return session_id

def initialize_agent():
    """Initialize the Sherlock Agent"""
    if not st.session_state.agent_initialized:
        try:
            with st.spinner("🚀 Initializing Sherlock Agent..."):
                from agent.workflow import Agent
                from agent.logger import get_logger
                
                logger = get_logger("streamlit_app")
                logger.info("Initializing Sherlock Agent from Streamlit UI")
                
                st.session_state.agent = Agent()
                st.session_state.agent.build_workflow()
                st.session_state.agent_initialized = True
                
                logger.info("Sherlock Agent successfully initialized in Streamlit UI")
                return True
        except Exception as e:
            st.error(f"❌ Failed to initialize agent: {str(e)}")
            return False
    return True

def sidebar():
    """Create the sidebar with session management"""
    st.sidebar.markdown("## 🔍 Sherlock Agent")
    st.sidebar.markdown("*AFEX's AI Data Analyst*")
    st.sidebar.divider()
    
    # Session Management
    st.sidebar.markdown("### 📝 Sessions")
    
    # Create new session button
    if st.sidebar.button("➕ New Session", use_container_width=True):
        create_new_session()
        st.rerun()
    
    # Session selector
    if st.session_state.sessions:
        session_options = {
            session_id: f"{data['name']} ({data['created_at'].strftime('%H:%M')})"
            for session_id, data in st.session_state.sessions.items()
        }
        
        selected_session = st.sidebar.selectbox(
            "Select Session:",
            options=list(session_options.keys()),
            format_func=lambda x: session_options[x],
            index=list(session_options.keys()).index(st.session_state.current_session_id) 
                  if st.session_state.current_session_id in session_options else 0
        )
        
        if selected_session != st.session_state.current_session_id:
            st.session_state.current_session_id = selected_session
            st.rerun()
        
        # Session stats
        if st.session_state.current_session_id:
            current_session = st.session_state.sessions[st.session_state.current_session_id]
            st.sidebar.markdown("#### 📊 Session Stats")
            st.sidebar.metric("Messages", len(current_session['messages']))
            st.sidebar.metric("Queries Executed", current_session['queries_executed'])
            st.sidebar.metric("Total Rows", current_session['total_rows_returned'])
    
    st.sidebar.divider()
    
    # Agent Status
    st.sidebar.markdown("### 🤖 Agent Status")
    if st.session_state.agent_initialized:
        st.sidebar.success("✅ Agent Ready")
    else:
        st.sidebar.warning("⚠️ Agent Not Initialized")
        if st.sidebar.button("🔄 Initialize Agent"):
            initialize_agent()
            st.rerun()
    
    # System Info
    st.sidebar.divider()
    st.sidebar.markdown("### ℹ️ System Info")
    st.sidebar.info("""
    **Sherlock Agent** helps you analyze AFEX's data by:
    - Understanding business questions
    - Writing optimized SQL queries
    - Executing queries safely
    - Providing actionable insights
    """)

def display_chat_message(message_type, content, timestamp=None):
    """Display a chat message with proper styling"""
    if timestamp is None:
        timestamp = datetime.now()
    
    if message_type == "user":
        st.markdown(f"""
        <div class="chat-message user-message">
            <strong>👤 You</strong> <small>({timestamp.strftime('%H:%M:%S')})</small><br>
            {content}
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="chat-message agent-message">
            <strong>🔍 Sherlock</strong> <small>({timestamp.strftime('%H:%M:%S')})</small><br>
            {content}
        </div>
        """, unsafe_allow_html=True)

def display_sql_query(sql_query):
    """Display SQL query with syntax highlighting"""
    st.markdown("#### 📝 Generated SQL Query")
    st.markdown(f"""
    <div class="sql-code">
        <code>{sql_query}</code>
    </div>
    """, unsafe_allow_html=True)

def display_data_results(data, sql_query):
    """Display query results with visualizations"""
    if not data or (isinstance(data, dict) and 'error' in data):
        st.error(f"❌ Query failed: {data.get('error', 'Unknown error')}")
        return 0
    
    df = pd.DataFrame(data)
    row_count = len(df)
    
    st.markdown("#### 📊 Query Results")
    
    # Display metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Rows Returned", row_count)
    with col2:
        st.metric("Columns", len(df.columns))
    with col3:
        execution_time = "< 1s"  # Placeholder - could be tracked from agent
        st.metric("Execution Time", execution_time)
    
    # Display data table
    st.dataframe(df, use_container_width=True)
    
    # Auto-generate simple visualizations for numeric data
    numeric_columns = df.select_dtypes(include=['number']).columns
    if len(numeric_columns) > 0 and row_count > 1:
        st.markdown("#### 📈 Quick Visualization")
        
        if len(numeric_columns) == 1:
            # Single numeric column - histogram
            fig = px.histogram(df, x=numeric_columns[0], title=f"Distribution of {numeric_columns[0]}")
            st.plotly_chart(fig, use_container_width=True)
        elif len(numeric_columns) >= 2:
            # Multiple numeric columns - scatter plot
            fig = px.scatter(df, x=numeric_columns[0], y=numeric_columns[1], 
                           title=f"{numeric_columns[0]} vs {numeric_columns[1]}")
            st.plotly_chart(fig, use_container_width=True)
    
    return row_count

def main_chat_interface():
    """Main chat interface"""
    st.markdown('<h1 class="main-header">🔍 Sherlock Agent - AFEX AI Data Analyst</h1>', 
                unsafe_allow_html=True)
    
    # Check if agent is initialized
    if not st.session_state.agent_initialized:
        st.warning("⚠️ Agent not initialized. Please initialize the agent from the sidebar.")
        return
    
    # Check if session exists
    if not st.session_state.current_session_id:
        st.info("👋 Welcome! Create a new session from the sidebar to start chatting with Sherlock.")
        return
    
    current_session = st.session_state.sessions[st.session_state.current_session_id]
    
    # Display chat history
    st.markdown("### 💬 Conversation")
    
    # Chat container
    chat_container = st.container()
    
    with chat_container:
        for message in current_session['messages']:
            display_chat_message(
                message['type'], 
                message['content'], 
                message['timestamp']
            )
            
            # Display SQL and results if available
            if message['type'] == 'agent' and 'sql_query' in message:
                if message['sql_query']:
                    display_sql_query(message['sql_query'])
                
                if 'data' in message and message['data']:
                    display_data_results(message['data'], message['sql_query'])
    
    # Chat input
    st.markdown("### ✍️ Ask Sherlock")
    user_input = st.text_area(
        "Enter your question about AFEX's data:",
        placeholder="e.g., What is the total number of farmers? Show me the top 10 commodities by volume.",
        height=100
    )
    
    col1, col2 = st.columns([1, 4])
    with col1:
        send_button = st.button("🚀 Send", use_container_width=True)
    with col2:
        if st.button("🗑️ Clear Session", use_container_width=True):
            current_session['messages'] = []
            current_session['queries_executed'] = 0
            current_session['total_rows_returned'] = 0
            st.rerun()
    
    # Process user input
    if send_button and user_input.strip():
        # Add user message
        user_message = {
            'type': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        }
        current_session['messages'].append(user_message)
        
        # Get agent response
        with st.spinner("🤔 Sherlock is thinking..."):
            try:
                response = st.session_state.agent.chat(user_input, st.session_state.current_session_id)
                
                # Add agent response
                agent_message = {
                    'type': 'agent',
                    'content': response['response'],
                    'timestamp': datetime.now(),
                    'sql_query': response.get('sql_query'),
                    'data': response.get('data')
                }
                current_session['messages'].append(agent_message)
                
                # Update session stats
                current_session['queries_executed'] += 1
                if response.get('data') and isinstance(response['data'], list):
                    current_session['total_rows_returned'] += len(response['data'])
                
            except Exception as e:
                st.error(f"❌ Error: {str(e)}")
                agent_message = {
                    'type': 'agent',
                    'content': f"I apologize, but I encountered an error: {str(e)}",
                    'timestamp': datetime.now()
                }
                current_session['messages'].append(agent_message)
        
        st.rerun()

def main():
    """Main application function"""
    initialize_session_state()
    
    # Initialize agent on startup
    if not st.session_state.agent_initialized:
        initialize_agent()
    
    # Create sidebar
    sidebar()
    
    # Main interface
    main_chat_interface()

if __name__ == "__main__":
    main()
