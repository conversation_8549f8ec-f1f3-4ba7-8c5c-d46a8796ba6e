#!/usr/bin/env python3
"""
Test script to demonstrate the logging functionality of the Sherlock Agent.
This script tests database connections and basic agent operations with comprehensive logging.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_database_connection():
    """Test database connection with logging"""
    print("=" * 60)
    print("🔍 TESTING DATABASE CONNECTION")
    print("=" * 60)
    
    try:
        from agent.db import connect_db, validate_sql_query, open_db_connection
        from agent.logger import get_logger
        
        logger = get_logger("test_logging")
        logger.info("Starting database connection test")
        
        # Test basic connection
        print("\n1. Testing basic database connection...")
        with open_db_connection() as conn:
            print("✅ Database connection successful!")
            
        # Test SQL validation
        print("\n2. Testing SQL query validation...")
        test_queries = [
            "SELECT 1",  # Valid query
            "SELECT * FROM nonexistent_table",  # Invalid table
            "INVALID SQL SYNTAX",  # Invalid syntax
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   Query {i}: {query}")
            result = validate_sql_query(query)
            if result['valid']:
                print(f"   ✅ Valid query")
            else:
                print(f"   ❌ Invalid query: {result['error'][:100]}...")
                
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False
    
    return True


def test_agent_initialization():
    """Test agent initialization with logging"""
    print("\n" + "=" * 60)
    print("🤖 TESTING AGENT INITIALIZATION")
    print("=" * 60)
    
    try:
        from agent.workflow import Agent
        from agent.logger import get_logger
        
        logger = get_logger("test_logging")
        logger.info("Starting agent initialization test")
        
        print("\n1. Initializing agent...")
        agent = Agent()
        print("✅ Agent initialized successfully!")
        
        print("\n2. Building workflow...")
        workflow = agent.build_workflow()
        print("✅ Workflow built successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization test failed: {e}")
        return False


def test_logging_features():
    """Test various logging features"""
    print("\n" + "=" * 60)
    print("📝 TESTING LOGGING FEATURES")
    print("=" * 60)
    
    try:
        from agent.logger import (
            get_logger, 
            log_database_operation, 
            log_agent_step, 
            log_error,
            log_function_call
        )
        
        logger = get_logger("test_logging")
        
        print("\n1. Testing basic logging...")
        logger.debug("This is a debug message")
        logger.info("This is an info message")
        logger.warning("This is a warning message")
        logger.error("This is an error message")
        
        print("\n2. Testing structured logging...")
        log_function_call("test_function", param1="value1", param2="value2")
        
        log_database_operation(
            "test_operation", 
            query="SELECT * FROM test_table",
            success=True,
            execution_time=0.123
        )
        
        log_agent_step(
            "test_step",
            state={"messages": ["test"], "command": "test"},
            additional_info="test data"
        )
        
        # Test error logging
        try:
            raise ValueError("This is a test error")
        except Exception as e:
            log_error(e, "test_logging", test_context="error handling test")
        
        print("✅ Logging features tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Logging features test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 SHERLOCK AGENT LOGGING TEST SUITE")
    print("=" * 60)
    print("This script tests the comprehensive logging system")
    print("Check the 'logs/' directory for detailed log files")
    print("=" * 60)
    
    # Ensure logs directory exists
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # Run tests
    tests = [
        ("Logging Features", test_logging_features),
        ("Database Connection", test_database_connection),
        ("Agent Initialization", test_agent_initialization),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\nTests passed: {passed}/{len(results)}")
    
    print("\n📁 Log files created:")
    for log_file in logs_dir.glob("*.log"):
        size = log_file.stat().st_size
        print(f"  - {log_file.name} ({size} bytes)")
    
    print("\n💡 Tips:")
    print("  - Check logs/agent.log for all application logs")
    print("  - Check logs/agent_errors.log for error-only logs")
    print("  - Check logs/database.log for database-specific logs")
    print("  - Logs are in JSON format for easy parsing")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
